# GameFlex Platform Design Document

## Overview

GameFlex is a comprehensive gaming social media platform architected as a modern, scalable system with a Flutter mobile frontend and serverless AWS backend. The platform enables gamers to share content, build communities, and engage through gaming-specific features like Reflexes (reactions), Xbox integration, and AI-powered content moderation.

The system follows a microservices architecture pattern using AWS Lambda functions, with a Flutter mobile application providing cross-platform support for iOS, Android, and desktop platforms. The design emphasizes performance, scalability, security, and user experience while maintaining cost-effectiveness through serverless technologies.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Flutter[Flutter Mobile App]
        Desktop[Desktop App]
    end
    
    subgraph "API Gateway Layer"
        API[AWS API Gateway]
        Auth[Custom Authorizer]
        CORS[CORS Handler]
    end
    
    subgraph "Compute Layer"
        AuthLambda[Auth Functions]
        PostsLambda[Posts Functions]
        MediaLambda[Media Functions]
        UsersLambda[Users Functions]
        ChannelsLambda[Channels Functions]
        ReflexesLambda[Reflexes Functions]
        XboxLambda[Xbox Functions]
    end
    
    subgraph "Storage Layer"
        Cognito[AWS Cognito]
        DynamoDB[DynamoDB Tables]
        S3[S3 Media Storage]
        CloudFront[CloudFront CDN]
    end
    
    subgraph "AI/ML Layer"
        Rekognition[AWS Rekognition]
        ContentMod[Content Moderation]
    end
    
    subgraph "External Services"
        Xbox[Xbox Live API]
        Apple[Apple Sign-In]
        NewRelic[New Relic Monitoring]
    end
    
    Flutter --> API
    Desktop --> API
    API --> Auth
    API --> AuthLambda
    API --> PostsLambda
    API --> MediaLambda
    API --> UsersLambda
    API --> ChannelsLambda
    API --> ReflexesLambda
    API --> XboxLambda
    
    AuthLambda --> Cognito
    PostsLambda --> DynamoDB
    MediaLambda --> S3
    MediaLambda --> CloudFront
    MediaLambda --> Rekognition
    UsersLambda --> DynamoDB
    ChannelsLambda --> DynamoDB
    ReflexesLambda --> DynamoDB
    XboxLambda --> Xbox
    XboxLambda --> DynamoDB
    
    S3 --> CloudFront
    Rekognition --> ContentMod
```

### Technology Stack

**Frontend (Flutter)**
- **Framework**: Flutter 3.7+ with Dart
- **State Management**: Provider pattern for reactive state management
- **HTTP Client**: http package with custom retry logic and authentication
- **Media Handling**: image_picker, video_player, pro_image_editor, pro_video_editor
- **Authentication**: AWS Cognito integration with Apple Sign-In support
- **Monitoring**: New Relic mobile monitoring
- **Caching**: flutter_cache_manager for media caching

**Backend (AWS Serverless)**
- **Compute**: AWS Lambda (Node.js 22.x runtime)
- **API Gateway**: AWS API Gateway with custom authorizers
- **Authentication**: AWS Cognito User Pools with JWT tokens
- **Database**: Amazon DynamoDB with GSI indexes
- **Storage**: Amazon S3 with CloudFront CDN
- **AI/ML**: AWS Rekognition for content moderation
- **Secrets**: AWS Secrets Manager for configuration
- **Infrastructure**: AWS CDK (TypeScript) for Infrastructure as Code

### Deployment Architecture

**Multi-Environment Support**
- **Development**: Local development environment for initial testing
- **Staging**: Pre-production environment for comprehensive testing
- **Production**: Scalable production deployment for live users

**Deployment Process**
- **Always use `deploy.sh` script** for all AWS backend deployments
- **Environment Commands**:
  ```bash
  # Development deployment
  ./deploy.sh development
  
  # Staging deployment  
  ./deploy.sh staging
  
  # Production deployment
  ./deploy.sh production
  ```
- **Deployment Options**: Use `--diff` to preview changes, `-y` to skip confirmations
- **Location**: All deployment commands must be run from the `backend/` directory

**CDN and Media Delivery**
- CloudFront distribution for global media delivery
- Custom domain support for branded URLs
- Optimized caching strategies for different content types

## Email Verification System

### Email Configuration Architecture

#### SES Configuration
```typescript
// Custom SES configuration for branded emails
const sesConfiguration = {
  emailSendingAccount: 'DEVELOPER',
  from: 'GameFlex <<EMAIL>>',
  replyToEmailAddress: '<EMAIL>',
  sourceArn: 'arn:aws:ses:us-east-1:ACCOUNT:identity/gameflex.io'
};
```

#### Email Template System
```typescript
interface EmailTemplate {
  subject: string;
  htmlBody: string;
  textBody: string;
  templateVariables: {
    verificationCode?: string;
    verificationLink?: string;
    username?: string;
    appName: string;
  };
}

// Template types
enum EmailTemplateType {
  VERIFICATION = 'verification',
  PASSWORD_RESET = 'password-reset',
  WELCOME = 'welcome'
}
```

#### Cognito Trigger Integration
```typescript
// Custom message trigger handler
export const customMessageTrigger = async (event: CognitoUserPoolTriggerEvent) => {
  const { triggerSource, request, response } = event;
  
  switch (triggerSource) {
    case 'CustomMessage_SignUp':
      response.emailMessage = await renderVerificationEmail(request);
      response.emailSubject = 'Verify your GameFlex account';
      break;
    case 'CustomMessage_ResendCode':
      response.emailMessage = await renderVerificationEmail(request);
      response.emailSubject = 'Your GameFlex verification code';
      break;
    case 'CustomMessage_ForgotPassword':
      response.emailMessage = await renderPasswordResetEmail(request);
      response.emailSubject = 'Reset your GameFlex password';
      break;
  }
  
  return event;
};
```

### Frontend Email Verification Flow

#### Verification Screen Components
```dart
// Email verification screen
class EmailVerificationScreen extends StatefulWidget {
  // Verification code input
  // Resend verification button
  // Auto-verification on app launch
  // Success/error state handling
}

// Verification code input widget
class VerificationCodeInput extends StatefulWidget {
  // 6-digit code input fields
  // Auto-focus and validation
  // Paste support for codes
  // Visual feedback for errors
}
```

#### Verification State Management
```dart
class EmailVerificationProvider extends ChangeNotifier {
  VerificationStatus _status = VerificationStatus.pending;
  String? _errorMessage;
  int _resendCooldown = 0;
  
  // Verify email with code
  Future<bool> verifyEmail(String code);
  
  // Resend verification email
  Future<bool> resendVerification();
  
  // Check verification status
  Future<void> checkVerificationStatus();
}

enum VerificationStatus {
  pending,
  verifying,
  verified,
  failed,
  expired
}
```

## Components and Interfaces

### Frontend Components

#### Core Application Structure
```dart
// Main application entry point
class MyApp extends StatelessWidget {
  // Multi-provider setup for state management
  // Theme configuration (light/dark mode)
  // Navigation and routing setup
}

// Splash screen and initialization
class SplashManager extends StatefulWidget {
  // Authentication state checking
  // Initial app configuration
  // Environment-specific setup
}
```

#### Authentication Components
```dart
// Main authentication provider
class AuthProvider extends ChangeNotifier {
  // User authentication state management
  // Token refresh handling
  // Multi-provider sign-in support (Email, Apple, Xbox)
}

// Authentication screens
class LoginScreen extends StatefulWidget {
  // Email/password authentication
  // Social sign-in options
  // Registration flow
}

class UsernameSelectionScreen extends StatefulWidget {
  // Username availability checking
  // Real-time validation
  // Profile completion
}
```

#### Content Management Components
```dart
// Posts provider for content management
class PostsProvider extends ChangeNotifier {
  // Feed management and caching
  // Post creation and editing
  // Engagement tracking
}

// Post composition interface
class PostCompositionScreen extends StatefulWidget {
  // Rich text editing
  // Media attachment handling
  // Channel selection
  // Draft management
}

// Media editing components
class ProImageEditorScreen extends StatefulWidget {
  // Advanced image editing capabilities
  // Filter and effect application
  // Crop and resize functionality
}
```

#### Social Interaction Components
```dart
// Reflex (reaction) system
class ReflexProvider extends ChangeNotifier {
  // Custom reaction management
  // Flare system integration
  // Real-time reaction updates
}

// Channel management
class ChannelsProvider extends ChangeNotifier {
  // Channel discovery and joining
  // Member management
  // Content organization
}
```

### Backend Lambda Functions

#### Authentication Services
```typescript
// User registration and sign-in
export const signupHandler = async (event: APIGatewayProxyEvent) => {
  // Cognito user creation
  // Email verification trigger
  // Initial profile setup
}

export const signinHandler = async (event: APIGatewayProxyEvent) => {
  // Credential validation
  // JWT token generation
  // Session management
}

// Apple Sign-In integration
export const appleSigninHandler = async (event: APIGatewayProxyEvent) => {
  // Apple ID token validation
  // User account linking
  // Profile data synchronization
}

// Email verification services
export const resendVerificationHandler = async (event: APIGatewayProxyEvent) => {
  // Resend verification email
  // Rate limiting for resend requests
  // Custom email template usage
}

export const confirmEmailHandler = async (event: APIGatewayProxyEvent) => {
  // Email confirmation processing
  // Account activation
  // Redirect to app or success page
}
```

#### Cognito Trigger Functions
```typescript
// Custom message trigger for branded emails
export const customMessageHandler = async (event: CognitoUserPoolTriggerEvent) => {
  // Custom email template rendering
  // Branded verification emails
  // Password reset emails
  // Welcome emails
}

// Pre-sign-up trigger for validation
export const preSignUpHandler = async (event: CognitoUserPoolTriggerEvent) => {
  // Email domain validation
  // Username format validation
  // Auto-confirm attributes
}
```

#### Content Management Services
```typescript
// Post management
export const postsHandler = async (event: APIGatewayProxyEvent) => {
  // CRUD operations for posts
  // Media attachment handling
  // Channel assignment
  // Engagement metrics tracking
}

// Media processing
export const mediaHandler = async (event: APIGatewayProxyEvent) => {
  // S3 upload URL generation
  // Media metadata management
  // Rekognition analysis integration
  // CDN URL generation
}
```

#### Social Features Services
```typescript
// Reflex system
export const reflexesHandler = async (event: APIGatewayProxyEvent) => {
  // Custom reaction creation
  // Reaction aggregation
  // Real-time updates
}

// Channel management
export const channelsHandler = async (event: APIGatewayProxyEvent) => {
  // Channel CRUD operations
  // Membership management
  // Content organization
}
```

### API Interface Design

#### RESTful API Structure
```
Base URL: https://api.gameflex.io (production)
Authentication: Bearer JWT tokens

Public Endpoints:
POST /auth/signup          - User registration
POST /auth/signin          - User authentication
POST /auth/apple-signin    - Apple Sign-In
POST /auth/confirm-email   - Email verification confirmation
POST /auth/resend-verification - Resend verification email
GET  /health              - Health check

Protected Endpoints:
GET    /auth/validate      - Token validation
POST   /auth/set-username  - Username setup

Posts:
GET    /posts              - Feed retrieval
POST   /posts              - Post creation
GET    /posts/{id}         - Post details
DELETE /posts/{id}         - Post deletion
POST   /posts/{id}/like    - Like/unlike post
POST   /posts/{id}/reactions - Emoji reactions

Media:
POST   /media/upload       - Upload URL generation
GET    /media/{id}         - Media metadata
POST   /media/{id}/process - Media processing

Users:
GET    /users/profile      - Current user profile
PUT    /users/profile      - Profile updates
GET    /users/{id}         - User profile
POST   /users/{id}/follow  - Follow/unfollow

Channels:
GET    /channels           - Channel listing
POST   /channels           - Channel creation
GET    /channels/{id}      - Channel details

Reflexes:
GET    /reflexes           - Reflex listing
POST   /reflexes           - Reflex creation
POST   /reflexes/{id}/like - Like reflex

Xbox Integration:
GET    /xbox/auth          - Xbox authentication
POST   /xbox/link          - Account linking
GET    /xbox/screenshots   - Xbox media retrieval
```

## Data Models

### User Management Schema

#### Users Table (DynamoDB)
```typescript
interface User {
  id: string;                    // Partition key (UUID)
  email: string;                 // GSI partition key
  username?: string;             // Unique username
  cognitoUserId: string;         // Cognito user ID
  firstName: string;
  lastName: string;
  createdAt: string;             // ISO timestamp
  updatedAt: string;             // ISO timestamp
}

// Global Secondary Indexes
// EmailIndex: email (PK)
// UsernameIndex: username (PK)
```

#### User Profiles Table (DynamoDB)
```typescript
interface UserProfile {
  userId: string;                // Partition key
  displayName?: string;
  bio?: string;
  avatarUrl?: string;
  followersCount: number;
  followingCount: number;
  postsCount: number;
  isVerified: boolean;
  preferences: {
    privacy: 'public' | 'private';
    notifications: boolean;
    darkMode: boolean;
  };
  createdAt: string;
  updatedAt: string;
}
```

### Content Management Schema

#### Posts Table (DynamoDB)
```typescript
interface Post {
  id: string;                    // Partition key (UUID)
  userId: string;                // GSI partition key
  channelId?: string;            // GSI partition key
  content: string;               // Post text content
  mediaUrl?: string;             // Media URL (S3/CloudFront)
  mediaType?: 'image' | 'video';
  mediaId?: string;              // Reference to media table
  likeCount: number;
  commentCount: number;
  reflexCount: number;
  isActive: boolean;
  status: 'draft' | 'uploading_media' | 'published';
  createdAt: string;             // GSI sort key
  updatedAt: string;
}

// Global Secondary Indexes
// UserIdIndex: userId (PK), createdAt (SK)
// ChannelIdIndex: channelId (PK), createdAt (SK)
// CreatedAtIndex: status (PK), createdAt (SK) - for feed generation
```

#### Media Table (DynamoDB)
```typescript
interface Media {
  id: string;                    // Partition key (UUID)
  userId: string;                // GSI partition key
  postId?: string;               // GSI partition key
  s3Bucket: string;
  s3Key: string;
  mediaType: 'image' | 'video';
  mimeType: string;
  fileSize: number;
  dimensions?: {
    width: number;
    height: number;
  };
  duration?: number;             // For videos
  status: 'uploading' | 'processing' | 'ready' | 'failed';
  rekognitionAnalysis?: {
    labels: Array<{
      name: string;
      confidence: number;
    }>;
    moderationLabels: Array<{
      name: string;
      confidence: number;
    }>;
    isAppropriate: boolean;
  };
  createdAt: string;
  updatedAt: string;
}
```

### Social Features Schema

#### Channels Table (DynamoDB)
```typescript
interface Channel {
  id: string;                    // Partition key (UUID)
  name: string;                  // GSI partition key (unique)
  description?: string;
  ownerId: string;               // GSI partition key
  isPublic: boolean;
  isActive: boolean;
  memberCount: number;
  postCount: number;
  iconUrl?: string;
  createdAt: string;
  updatedAt: string;
}
```

#### Reflexes Table (DynamoDB)
```typescript
interface Reflex {
  id: string;                    // Partition key (UUID)
  postId: string;                // GSI partition key
  userId: string;                // GSI partition key
  mediaId?: string;              // Custom media reflex
  flareData?: {                  // Predefined flare reflex
    type: string;
    category: string;
    imageUrl: string;
  };
  textOverlay?: string;
  reflexType: 'flare' | 'custom_image';
  likes: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

#### Engagement Tables (DynamoDB)
```typescript
interface Like {
  id: string;                    // Partition key (UUID)
  userId: string;                // GSI partition key
  postId?: string;               // GSI partition key
  reflexId?: string;             // GSI partition key
  createdAt: string;
}

interface Comment {
  id: string;                    // Partition key (UUID)
  postId: string;                // GSI partition key
  userId: string;                // GSI partition key
  content: string;
  parentCommentId?: string;      // For threaded comments
  likes: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PostReaction {
  id: string;                    // Partition key (UUID)
  postId: string;                // GSI partition key
  userId: string;                // GSI partition key
  emoji: string;                 // Emoji character
  createdAt: string;
}
```

### Gaming Integration Schema

#### Xbox Accounts Table (DynamoDB)
```typescript
interface XboxAccount {
  id: string;                    // Partition key (UUID)
  userId: string;                // GSI partition key
  xboxUserId: string;            // Xbox Live user ID
  gamertag: string;
  displayName: string;
  avatarUrl?: string;
  accessToken: string;           // Encrypted
  refreshToken: string;          // Encrypted
  tokenExpiresAt: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

## Error Handling

### Frontend Error Handling Strategy

#### Network Error Handling
```dart
class ApiService {
  // Automatic retry logic for network failures
  // Exponential backoff for rate limiting
  // Token refresh on 401 errors
  // User-friendly error messages
  
  Future<http.Response> _makeRequestWithRetry({
    required String method,
    required String path,
    int attempt = 1,
  }) async {
    try {
      // Request implementation
    } catch (e) {
      if (_isNetworkError(e) && attempt < _maxRetries) {
        await Future.delayed(Duration(seconds: attempt));
        return _makeRequestWithRetry(attempt: attempt + 1);
      }
      rethrow;
    }
  }
}
```

#### User Experience Error Handling
```dart
class AuthProvider extends ChangeNotifier {
  // Graceful error state management
  // User-friendly error messages
  // Automatic recovery mechanisms
  
  void _setErrorWithoutStatusChange(String error) {
    _errorMessage = error;
    if (_status == AuthStatus.loading) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }
}
```

### Backend Error Handling Strategy

#### Lambda Function Error Handling
```typescript
export const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  },
  body: JSON.stringify(body)
});

export const handler = async (event: APIGatewayProxyEvent) => {
  try {
    // Business logic
  } catch (error) {
    console.error('Handler error:', error);
    
    if (error.name === 'ValidationError') {
      return createResponse(400, { error: 'Invalid input', details: error.message });
    }
    
    if (error.name === 'NotAuthorizedException') {
      return createResponse(401, { error: 'Authentication failed' });
    }
    
    return createResponse(500, { error: 'Internal server error' });
  }
};
```

#### Database Error Handling
```typescript
// DynamoDB error handling with retry logic
const dynamoRetry = async (operation: () => Promise<any>, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (error.name === 'ProvisionedThroughputExceededException' && attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        continue;
      }
      throw error;
    }
  }
};
```

## Testing Strategy

### Frontend Testing Approach

**Platform-Specific Testing Requirements**
- **Primary Platform**: iOS on macOS development environment
- **Testing Device**: **ALWAYS test on physical iOS device, NOT simulator**
- **Build Target**: Use iOS build configuration for all testing
- **Device Requirements**: Ensure iOS device is connected and properly configured for development

#### Unit Testing
```dart
// Widget testing for UI components
testWidgets('Login screen displays correctly', (WidgetTester tester) async {
  await tester.pumpWidget(MaterialApp(home: LoginScreen()));
  
  expect(find.text('Email'), findsOneWidget);
  expect(find.text('Password'), findsOneWidget);
  expect(find.byType(ElevatedButton), findsWidgets);
});

// Provider testing for state management
test('AuthProvider handles sign in correctly', () async {
  final authProvider = AuthProvider();
  
  when(mockAuthService.signIn(any, any))
    .thenAnswer((_) async => AuthResponse(success: true));
  
  final result = await authProvider.signIn(email: '<EMAIL>', password: 'password');
  
  expect(result, true);
  expect(authProvider.status, AuthStatus.authenticated);
});
```

#### Integration Testing
```dart
// End-to-end testing for critical user flows
// NOTE: Run these tests on physical iOS device only
testWidgets('Complete authentication flow', (WidgetTester tester) async {
  await tester.pumpWidget(MyApp());
  
  // Navigate through sign up flow
  await tester.tap(find.text('Sign Up'));
  await tester.pumpAndSettle();
  
  // Fill in registration form
  await tester.enterText(find.byKey(Key('email')), '<EMAIL>');
  await tester.enterText(find.byKey(Key('password')), 'password123');
  
  // Submit and verify success
  await tester.tap(find.text('Create Account'));
  await tester.pumpAndSettle();
  
  expect(find.text('Welcome'), findsOneWidget);
});
```

#### Device Testing Commands
```bash
# Run tests on connected iOS device
flutter test integration_test/app_test.dart -d <device-id>

# Build and run on iOS device for manual testing
flutter run -d <device-id>

# Get connected device ID
flutter devices
```

### Backend Testing Approach

#### Unit Testing
```typescript
// Lambda function unit tests
describe('Posts Handler', () => {
  test('creates post successfully', async () => {
    const event = {
      httpMethod: 'POST',
      body: JSON.stringify({
        content: 'Test post',
        channelId: 'channel-123'
      }),
      requestContext: {
        authorizer: { userId: 'user-123' }
      }
    };
    
    const result = await postsHandler(event);
    
    expect(result.statusCode).toBe(201);
    expect(JSON.parse(result.body)).toHaveProperty('id');
  });
});
```

#### Integration Testing
```typescript
// API integration tests
describe('Authentication Flow', () => {
  test('complete sign up and sign in flow', async () => {
    // Test user registration
    const signupResponse = await request(apiUrl)
      .post('/auth/signup')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expect(signupResponse.status).toBe(201);
    
    // Test user sign in
    const signinResponse = await request(apiUrl)
      .post('/auth/signin')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expect(signinResponse.status).toBe(200);
    expect(signinResponse.body).toHaveProperty('tokens');
  });
});
```

### Performance Testing

#### Load Testing Strategy
```typescript
// API Gateway and Lambda performance testing
const loadTest = {
  scenarios: {
    'feed-browsing': {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '2m', target: 100 },
        { duration: '5m', target: 100 },
        { duration: '2m', target: 0 }
      ]
    }
  },
  thresholds: {
    'http_req_duration': ['p(95)<500'],
    'http_req_failed': ['rate<0.1']
  }
};
```

#### Mobile Performance Testing
```dart
// Flutter performance testing
test('Feed scrolling performance', () async {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  await binding.traceAction(() async {
    // Simulate feed scrolling
    await tester.fling(find.byType(ListView), Offset(0, -500), 1000);
    await tester.pumpAndSettle();
  }, reportKey: 'feed_scroll_performance');
  
  // Verify frame rendering performance
  expect(binding.framePolicy, equals(LiveTestWidgetsFlutterBindingFramePolicy.fullyLive));
});
```

## Security Considerations

### Authentication Security
- **JWT Token Management**: Secure token storage with automatic refresh
- **Multi-Factor Authentication**: Email verification for account security
- **Custom Email Templates**: Branded verification emails using custom SES configuration
- **Email Domain Verification**: Use verified gameflex.io domain for all authentication emails
- **OAuth Integration**: Secure Apple Sign-In and Xbox Live integration
- **Session Management**: Automatic token expiration and refresh handling

### Email Configuration
- **SES Integration**: Custom SES configuration with verified gameflex.io domain
- **Email Templates**: HTML templates for verification, password reset, and welcome emails
- **Cognito Triggers**: Custom message triggers for branded email experience
- **Rate Limiting**: Prevent email spam with resend rate limiting
- **Email Tracking**: Monitor email delivery and engagement metrics

### API Security
- **Custom Authorizers**: Lambda-based JWT validation for all protected endpoints
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Rate Limiting**: API Gateway throttling to prevent abuse
- **Input Validation**: Comprehensive request validation and sanitization

### Data Security
- **Encryption at Rest**: DynamoDB and S3 encryption
- **Encryption in Transit**: HTTPS/TLS for all communications
- **Secrets Management**: AWS Secrets Manager for sensitive configuration
- **Access Control**: IAM roles and policies with least privilege principle

### Content Security
- **AI Content Moderation**: AWS Rekognition for automated content analysis
- **User Reporting**: Community-driven content flagging system
- **Content Review**: Manual review process for flagged content
- **Privacy Controls**: User-configurable privacy settings and content visibility